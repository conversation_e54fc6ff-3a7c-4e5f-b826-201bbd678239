FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    sqlite3 \
    iputils-ping \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY requirements.txt .
COPY app.py .
COPY microservices/ ./microservices/
COPY database/ ./database/
COPY static/ ./static/
COPY templates/ ./templates/

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建数据库目录
RUN mkdir -p /app/data

# 初始化数据库
RUN python -c "from app import init_db; init_db()"

# 暴露端口
EXPOSE 5000

# 启动应用
CMD ["python", "app.py"]
